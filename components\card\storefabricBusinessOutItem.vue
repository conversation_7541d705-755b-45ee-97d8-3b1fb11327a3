<template>
  <view class="card-container" @click="gotoDetailFun">
    <!-- 顶部：单号和日期 -->
    <view class="card-header">
      <view class="bill-no">{{item.BillNo}}</view>
      <view class="bill-date">{{item.BillDate}}</view>
    </view>

    <!-- 中间：单据类型标签 -->
    <view class="card-middle">
      <view class="bill-type-tag">{{item.BillTypeName}}</view>
      <view class="status-tag">{{item.CommitStatus == '1' ? '已审核' : '待审核'}}</view>
    </view>

    <!-- 底部：主要信息 -->
    <view class="card-bottom">
      <view class="info-row">
        <view class="info-item">
          <view class="info-label">出仓单位</view>
          <view class="info-value">{{item.StoreName}}</view>
        </view>
        <view class="info-item">
          <view class="info-label">接收单位</view>
          <view class="info-value">{{item.CustomerName}}</view>
        </view>
      </view>
      <view class="info-row">
        <view class="info-item">
          <view class="info-label">匹数</view>
          <view class="info-value">{{item.TotalRoll || 0}} 匹</view>
        </view>
        <view class="info-item">
          <view class="info-label">数量</view>
          <view class="info-value">{{item.TotalQty || 0}} 米</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>	
  export default {
    props: {
      item: {
        type: Object,
        default: () => {}
      },
      index: {
        type: Number,
        default: 0
      },
      isSelect: {
        type: Boolean,
        default: false
      },
      isDetail: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        
      }
    },
    methods: {
      gotoDetailFun: function() {
        if(this.isSelect) {
          return
        }
        uni.$bjInfo = this.item;
        uni.navigateTo({
          url: '/pages/storefabric/storefabricBusinessOutDetail?billid=' + this.item.BillMasterID
        })
      }
    }
  }
</script>

<style>
.card-container {
  background-color: #FFFFFF;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
}

/* 顶部：单号和日期 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.bill-no {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.bill-date {
  font-size: 28rpx;
  color: #999999;
}

/* 中间：标签区域 */
.card-middle {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 16rpx;
}

.bill-type-tag {
  background-color: #e8f5e8;
  color: #52c41a;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-tag {
  background-color: #f0f0f0;
  color: #666666;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

/* 底部：信息区域 */
.card-bottom {
  margin-top: 8rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
}

.info-label {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}
</style>
